import { ABCApiNetwork } from "../net";

export interface AsrResult {
    id: string;
    taskId: string;
    businessId: string;
    businessType: number;
    supplierId: string;
    results: any;
    result: any; // 语音结果
    aiResult: any;
    voiceUrls: string[];
    duration: number;
    created: string;
}

export class Asr {
    /**
     * 获取语音识别结果列表
     */
    static async getAsrResult(businessId: string, taskId: string, businessType?: number[]): Promise<AsrResult[]> {
        try {
            const res: any = await ABCApiNetwork.post(`asr/result/query`, {
                body: {
                    businessId: businessId || "",
                    taskId: taskId || "",
                    businessTypes: businessType || [0, 1],
                },
            });
            return res?.rows;
        } catch (error) {
            console.error("Get asr result error:", error);
            return [];
        }
    }

    static async getAsrResultById(id: string): Promise<AsrResult> {
        try {
            return await ABCApiNetwork.get(`asr/result/${id}`, {});
        } catch (error) {
            console.error("Get asr result error:", error);
            return {} as AsrResult;
        }
    }

    static async getAsrResultByTaskId(taskId: string, businessId?: string, businessType?: number[]): Promise<AsrResult> {
        try {
            return await ABCApiNetwork.post(`asr/result/task/${taskId}`, {
                body: {
                    businessId: businessId || "",
                    taskId: taskId || "",
                    businessTypes: businessType || [0, 1],
                },
            });
        } catch (error) {
            console.error("Get asr result error:", error);
            return {} as AsrResult;
        }
    }

    /**
     * 保存语音识别结果
     */
    static async saveAsrResult(id: string, aiResult: string): Promise<any> {
        try {
            console.log("Save asr result:", id, aiResult);
            return await ABCApiNetwork.put(`asr/result/ai-result/${id}`, {
                body: {
                    aiResult: aiResult || "",
                },
            });
        } catch (error) {
            console.error("Save asr result error:", error);
        }
    }
}
