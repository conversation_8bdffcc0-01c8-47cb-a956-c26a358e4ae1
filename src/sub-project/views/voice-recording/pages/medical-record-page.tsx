/**
 * 病历转写页面
 */
import React from "react";
import { BasePage, SizedBox } from "../../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import type { RenderMethodsProps } from "../utils/types";

import { Text, View } from "@hippy/react";
import { renderMedicalRecordView } from "../renders/medical-record-renders";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import AbcAudioView from "../../../base-ui/views/abc-audio-view";
import { VoiceRecordingPageViewState } from "../utils/types";
import { VoiceRecordingOverlayManager } from "./voice-recording-overlay-manager";
import { OutpatientDraftManager } from "../../../outpatient/data/outpatient-drafts";
import { OutpatientUtils } from "../../../outpatient/utils/outpatient-utils";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";
import { OutpatientInvoiceStatus } from "../../../outpatient/data/outpatient-beans";

export class MedicalRecordPage extends BasePage<RenderMethodsProps, VoiceRecordingPageViewState> {
    private isAudioWaveInfoGet = false;
    private playbackTimer: NodeJS.Timeout | null = null;

    constructor(props: RenderMethodsProps) {
        super(props);
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        this.state = {
            ...managerState,
        };
    }

    componentDidMount(): void {
        super.componentDidMount();
        VoiceRecordingOverlayManager.instance.setAsrOverlayRecordingRef(this);
    }

    componentWillUnmount(): void {
        super.componentWillUnmount();
        this.stopPlaybackTimer();
        const voiceViewRef = VoiceRecordingOverlayManager.instance.getVoiceViewRef();
        if (voiceViewRef) {
            voiceViewRef.pause();
        }
        VoiceRecordingOverlayManager.instance.setState({
            medicalRecordData: "",
        });
    }

    getAppBarTitle(): string {
        return "";
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return (
            <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp44 }]}>
                <View style={{ alignItems: "center" }}>
                    <Text
                        style={[
                            this.state.currentTab === "voice" ? TextStyles.t16MB : TextStyles.t14NT2,
                            {
                                width: Sizes.dp72,
                                textAlign: "center",
                            },
                        ]}
                        onClick={() => this.handleTabChange("voice")}
                    >
                        语音记录
                    </Text>
                    {this.state.currentTab === "voice" && (
                        <View
                            style={{
                                width: Sizes.dp30,
                                height: Sizes.dp3,
                                backgroundColor: Colors.B1,
                                position: "absolute",
                                bottom: -Sizes.dp11,
                                borderRadius: Sizes.dp2,
                            }}
                        />
                    )}
                </View>
                <SizedBox width={Sizes.dp24} />
                <View style={{ alignItems: "center" }}>
                    <Text
                        style={[
                            this.state.currentTab === "medical" ? TextStyles.t16MB : TextStyles.t14NT2,
                            {
                                width: Sizes.dp72,
                                textAlign: "center",
                            },
                        ]}
                        onClick={() => this.handleTabChange("medical")}
                    >
                        病历转写
                    </Text>
                    {this.state.currentTab === "medical" && (
                        <View
                            style={{
                                width: Sizes.dp30,
                                height: Sizes.dp3,
                                backgroundColor: Colors.B1,
                                position: "absolute",
                                bottom: -Sizes.dp11,
                                borderRadius: Sizes.dp2,
                            }}
                        />
                    )}
                </View>
            </View>
        );
    }

    getStatusBarColor() {
        return Colors.white;
    }

    getAppBarBgColor() {
        return Colors.white;
    }

    onBackClick(): void {
        OutpatientUtils.voiceRecordingAsrResult.next(true);
        console.log("back---------");
        ABCNavigator.pop();
    }

    // 返回录制页面
    private handleBackToRecord = () => {
        VoiceRecordingOverlayManager.instance.handleBackToRecord();
    };

    // Tab切换
    private handleTabChange = (tab: "voice" | "medical") => {
        VoiceRecordingOverlayManager.instance.setState({ currentTab: tab });
    };

    // ---------------语音播放控制相关方法
    private handlePlayPause = () => {
        const currentState = VoiceRecordingOverlayManager.instance.getState();
        const { isPlaying } = currentState;

        VoiceRecordingOverlayManager.instance.setState({
            isPlaying: !isPlaying,
        });

        if (!isPlaying) {
            const voiceViewRef = VoiceRecordingOverlayManager.instance.getVoiceViewRef();
            if (voiceViewRef) {
                voiceViewRef?.play(currentState.synthesizedFilePath);
            }
            if (currentState.currentPlayTime == currentState.totalDuration) {
                this.stopPlaybackTimer();
                VoiceRecordingOverlayManager.instance.setState({ currentPlayTime: 0 });
            }
            this.startPlaybackTimer();
        } else {
            const voiceViewRef = VoiceRecordingOverlayManager.instance.getVoiceViewRef();
            if (voiceViewRef) {
                voiceViewRef.pause();
            }
            this.stopPlaybackTimer();
        }
    };

    private handleSeekTo = (time: number) => {
        const currentState = VoiceRecordingOverlayManager.instance.getState();
        const { voiceRecordSegments, totalDuration } = currentState;
        const clampedTime = Math.max(0, Math.min(time, totalDuration));

        const targetSegment =
            voiceRecordSegments.find((segment: any) => clampedTime >= segment.startTime && clampedTime < segment.endTime) ||
            voiceRecordSegments.find((segment: any) => Math.abs(clampedTime - segment.startTime) / 1000 < 0.5);

        const voiceViewRef = VoiceRecordingOverlayManager.instance.getVoiceViewRef();
        if (voiceViewRef) {
            voiceViewRef.seekTo(clampedTime);
        }

        VoiceRecordingOverlayManager.instance.setState({
            currentPlayTime: clampedTime,
            highlightedSegmentId: targetSegment ? targetSegment.id : null,
        });
    };

    private handleSpeedChange = (speed: number) => {
        VoiceRecordingOverlayManager.instance.setState({ playbackSpeed: speed });
        const voiceViewRef = VoiceRecordingOverlayManager.instance.getVoiceViewRef();
        voiceViewRef?.setPlaybackSpeed(speed);
    };

    private handleSegmentClick = (segmentId: string) => {
        const currentState = VoiceRecordingOverlayManager.instance.getState();
        const { voiceRecordSegments } = currentState;
        const segment = voiceRecordSegments.find((s: any) => s.id === segmentId);

        if (segment) {
            VoiceRecordingOverlayManager.instance.setState({
                highlightedSegmentId: segmentId,
                currentPlayTime: segment.startTime,
            });
        }
    };

    // 播放进度定时器管理
    private startPlaybackTimer = () => {
        this.stopPlaybackTimer(); // 确保没有重复的定时器

        this.playbackTimer = setInterval(() => {
            const currentState = VoiceRecordingOverlayManager.instance.getState();
            const { currentPlayTime, totalDuration, playbackSpeed, voiceRecordSegments } = currentState;
            const nextTime = currentPlayTime + 50 * playbackSpeed;

            if (nextTime >= totalDuration) {
                // 播放结束
                VoiceRecordingOverlayManager.instance.setState({
                    currentPlayTime: totalDuration,
                    isPlaying: false,
                    highlightedSegmentId: null,
                });
                this.stopPlaybackTimer();
            } else {
                // 根据当前时间找到对应的片段
                const currentSegment =
                    voiceRecordSegments.find((segment: any) => nextTime >= segment.startTime && nextTime < segment.endTime) ||
                    voiceRecordSegments.find((segment: any) => Math.abs(nextTime - segment.startTime) < 500);

                VoiceRecordingOverlayManager.instance.setState({
                    currentPlayTime: nextTime,
                    highlightedSegmentId: currentSegment ? currentSegment.id : null,
                });
            }
        }, 50);
    };

    private stopPlaybackTimer = () => {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
    };

    // ---------------语音播放控制相关方法

    // 采纳病历
    handleAcceptClick(): void {
        const AllMRKey = [
            {
                key: "chiefComplaint",
                label: "主诉",
                matchLabel: "主诉",
            },
            {
                key: "presentHistory",
                label: "现病史",
                matchLabel: "现病史",
            },
            {
                key: "pastHistory",
                label: "既往史",
                matchLabel: "既往史",
            },
            {
                key: "familyHistory",
                label: "家族史",
                matchLabel: "家族史",
            },
            {
                key: "allergicHistory",
                label: "过敏史",
                matchLabel: "过敏史",
            },
            {
                key: "personalHistory",
                label: "个人史",
                matchLabel: "个人史",
            },
            {
                key: "obstetricalHistory",
                label: "月经婚育史",
                matchLabel: "月经婚育史",
            },
            {
                key: "physicalExamination",
                label: "体格检查",
                matchLabel: "体格检查",
            },
            {
                key: "chineseExamination",
                label: "望闻切诊",
                matchLabel: "望闻切诊",
            },
            {
                key: "oralExamination",
                label: "口腔检查",
                matchLabel: "口腔检查",
            },
            {
                key: "auxiliaryExaminations",
                label: "辅助检查",
                matchLabel: "辅助检查",
            },
            {
                key: "diagnosis",
                label: "诊断",
                matchLabel: "诊断",
            },
            {
                key: "syndrome",
                label: "辨证",
                matchLabel: "辨证",
            },
            {
                key: "therapy",
                label: "治法",
                matchLabel: "治法",
            },
            {
                key: "disposals",
                label: "处置",
                matchLabel: "处置-口腔",
            },
        ];

        try {
            const currentState = VoiceRecordingOverlayManager.instance.getState();
            if (currentState.medicalRecordData == "未识别到有效病历内容") {
                VoiceRecordingOverlayManager.instance.navigateDialog({
                    title: "提示",
                    content: "未识别到有效病历内容",
                });
                delayed(1000)
                    .toPromise()
                    .then(() => {
                        ABCNavigator.pop();
                    });
                return;
            }

            // 解析病历内容
            const medicalRecordData = VoiceRecordingOverlayManager.instance.parseMedicalRecord(currentState.medicalRecordData, AllMRKey);

            // 判断上一级页面是否是挂号页面
            const allRoutes = ABCNavigator.getAllRoutes();
            if (
                allRoutes.length > 0 &&
                allRoutes.find(
                    (item: any) => item.routeName.includes("outpatientSheet") || item.routeName.includes("OutpatientInvoicePage")
                )
            ) {
                OutpatientUtils.voiceRecordingAsrAllow.next({ medicalRecordData });
                OutpatientUtils.voiceRecordingAsrResult.next(true);
                ABCNavigator.pop();
            } else {
                // 写入缓存
                const draftManager = OutpatientDraftManager.getInstance(true);
                VoiceRecordingOverlayManager.instance.draft.medicalRecord = medicalRecordData;
                VoiceRecordingOverlayManager.instance.draft.status = OutpatientInvoiceStatus.waitVisit;
                draftManager.saveDraft(VoiceRecordingOverlayManager.instance.draft, false).then(() => {
                    ABCNavigator.pop();
                });
            }
        } catch (error) {
            console.error("采纳病历失败:", error);
        }
    }

    renderContent(): JSX.Element {
        // 从管理器获取最新状态
        const managerState = VoiceRecordingOverlayManager.instance.getState();

        const prop = {
            state: managerState,
            setVoiceViewRef: (ref: AbcAudioView | null) => {
                VoiceRecordingOverlayManager.instance.setVoiceViewRef(ref);
                if (!this.isAudioWaveInfoGet && managerState.voiceRecordResult?.voiceUrls) {
                    this.isAudioWaveInfoGet = true;
                    VoiceRecordingOverlayManager.instance
                        .synthesizeVoiceRecording(managerState.voiceRecordResult.voiceUrls)
                        .then((synthesizedFilePath) => {
                            VoiceRecordingOverlayManager.instance.setState({ synthesizedFilePath });
                        });
                }
            },
            // 语音播放控制回调
            onPlayPause: this.handlePlayPause,
            onSeekTo: this.handleSeekTo,
            onSpeedChange: this.handleSpeedChange,
            onSegmentClick: this.handleSegmentClick,
            onBackToRecord: this.handleBackToRecord,
            onTabChange: this.handleTabChange,
            onAcceptClick: this.handleAcceptClick.bind(this),
        };
        return renderMedicalRecordView(prop);
    }
}
