/**
 * 语音录制进度全局浮窗组件
 * 参考AppLogOverlayView实现
 * create date 2024/06/16
 */
import { Animation } from "@hippy/react";
import { BaseComponent } from "../../../base-ui/base-component";
import { sharedPreferences } from "../../../base-business/preferences/shared-preferences";
import AbcAudioView from "../../../base-ui/views/abc-audio-view";
import type { VoiceRecordingOverlayViewProps, VoiceRecordingOverlayViewState, TouchPosition } from "../utils/types";
import { VoiceRecordingOverlayViewLocal, TAG, parsePositionData, ensureSafePosition } from "../utils/voice-recording-utils";
import { VoiceRecordingOverlayManager } from "./voice-recording-overlay-manager";
import { renderFullscreenView } from "../renders/fullscreen-view-renders";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";

export default class VoiceRecordingPage extends BaseComponent<VoiceRecordingOverlayViewProps, VoiceRecordingOverlayViewState> {
    touchPointPosition: TouchPosition = { top: 0, right: 0 };
    private hippyEventEmitterHandler: any;
    private fullscreenOpacityAnimation: Animation | null = null; // 全屏视图透明度动画
    private minimizedOpacityAnimation: Animation | null = null; // 最小化视图透明度动画
    private audioViewRef: AbcAudioView | null = null; // 音频播放组件引用

    constructor(props: VoiceRecordingOverlayViewProps) {
        super(props);

        // 获取保存的位置
        const savedPosition = sharedPreferences.getObject(VoiceRecordingOverlayViewLocal);
        const parsedPosition = parsePositionData(savedPosition);
        const initialPosition = ensureSafePosition(parsedPosition, 80, 80);
        console.log(TAG, "constructor - initialPosition:", initialPosition, "isMinimized:", props.isMinimized ?? true);

        // 从管理器获取初始状态，只保留位置相关的本地状态
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        this.state = {
            ...managerState,
            // 保留位置相关的本地状态
            position: initialPosition,
        };
        this.initializeAnimations();
    }

    private initializeAnimations(): void {
        // 初始化全屏视图透明度动画
        this.fullscreenOpacityAnimation = new Animation({
            startValue: 0,
            toValue: this.state.fullscreenOpacity,
            duration: 300,
            mode: "timing",
            timingFunction: "ease-in-out",
        });

        // 初始化最小化视图透明度动画
        this.minimizedOpacityAnimation = new Animation({
            startValue: 0,
            toValue: this.state.minimizedOpacity,
            duration: 300,
            mode: "timing",
            timingFunction: "ease-in-out",
        });
    }

    componentDidMount() {
        super.componentDidMount();
        // 注册到管理器
        VoiceRecordingOverlayManager.instance.setAsrRecordingRef(this);

        // 从管理器同步初始状态
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        this.setState({
            ...managerState,
            // 保留位置相关的本地状态
            position: this.state.position,
        });
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        // 计时器由管理器统一管理，在视图切换时应该继续运行
        if (this.hippyEventEmitterHandler) {
            this.hippyEventEmitterHandler.remove();
        }
        // 清理动画
        if (this.fullscreenOpacityAnimation) {
            this.fullscreenOpacityAnimation.destroy();
            this.fullscreenOpacityAnimation = null;
        }
        if (this.minimizedOpacityAnimation) {
            this.minimizedOpacityAnimation.destroy();
            this.minimizedOpacityAnimation = null;
        }
    }

    componentDidUpdate(prevProps: VoiceRecordingOverlayViewProps, prevState: VoiceRecordingOverlayViewState) {
        this.checkAndTriggerFadeInAnimation(prevState);
    }

    private checkAndTriggerFadeInAnimation = (prevState: VoiceRecordingOverlayViewState) => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        const shouldShowFullscreen = !managerState.isMinimized && !managerState.showMedicalRecordView && managerState.showRecord;
        const wasShowingFullscreen = !prevState.isMinimized && !prevState.showMedicalRecordView && prevState.showRecord;

        const shouldShowMinimized = managerState.isMinimized && !managerState.showMedicalRecordView && managerState.showRecord;
        const wasShowingMinimized = prevState.isMinimized && !prevState.showMedicalRecordView && prevState.showRecord;
        if (shouldShowFullscreen && !wasShowingFullscreen) {
            this.animateFullscreenOpacity(1);
        }

        if (shouldShowMinimized && !wasShowingMinimized) {
            this.animateMinimizedOpacity(1);
        }
    };

    private handleToggleSize = () => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        const newMinimizedState = !managerState.isMinimized;

        console.log("[ToggleDebug] handleToggleSize", {
            currentMinimized: managerState.isMinimized,
            newMinimizedState,
            isRecording: managerState.isRecording,
            duration: managerState.duration,
            isPaused: managerState.isPaused,
            waveformDataLength: managerState.waveformData?.length || 0,
        });

        if (newMinimizedState) {
            VoiceRecordingOverlayManager.instance.setState({
                isMinimized: true,
            });
            VoiceRecordingOverlayManager.instance.showMinimizedOverlay();
            ABCNavigator.pop();
        } else {
            VoiceRecordingOverlayManager.instance.setState({
                isMinimized: false,
                fullscreenOpacity: 1,
                minimizedOpacity: 0,
            });
        }

        this.props.onToggleFullscreen?.();
    };

    private animateFullscreenOpacity = (toValue: number, onComplete?: () => void) => {
        if (!this.fullscreenOpacityAnimation) {
            return;
        }

        // 更新动画配置
        this.fullscreenOpacityAnimation.updateAnimation({
            startValue: 0,
            toValue: toValue,
            duration: 300,
            mode: "timing",
            timingFunction: "ease-in-out",
        });

        // 设置动画结束回调
        this.fullscreenOpacityAnimation.onAnimationEnd(() => {
            VoiceRecordingOverlayManager.instance.setState({ minimizedOpacity: 0 });
            onComplete?.();
        });

        // 开始动画
        this.fullscreenOpacityAnimation.start();
    };

    private animateMinimizedOpacity = (toValue: number, onComplete?: () => void) => {
        if (!this.minimizedOpacityAnimation) {
            return;
        }

        // 更新动画配置
        this.minimizedOpacityAnimation.updateAnimation({
            startValue: 0,
            toValue: toValue,
            duration: 300,
            mode: "timing",
            timingFunction: "ease-in-out",
        });

        // 设置动画结束回调
        this.minimizedOpacityAnimation.onAnimationEnd(() => {
            VoiceRecordingOverlayManager.instance.setState({ fullscreenOpacity: 1 });
            onComplete?.();
        });

        // 开始动画
        this.minimizedOpacityAnimation.start();
    };
    // ---------------播放音效 (已移到管理器中)

    // ---------------录制
    // 关闭录制(窗口关闭)
    private handleCloseRecord = () => {
        // 直接停止录制并隐藏浮窗，不播放音效
        VoiceRecordingOverlayManager.instance.handleStopRecord(false);
    };
    // 停止录制（结束按钮） - 委托给管理器
    private handleStopRecord = async () => {
        VoiceRecordingOverlayManager.instance.handleStopRecord(true);
    };

    // 暂停开始录制 (暂停开始按钮) - 委托给管理器
    private handleStartPauseRecord = async (): Promise<void> => {
        VoiceRecordingOverlayManager.instance.handleStartPauseRecord();
    };

    // 触摸事件处理
    private handleTouchDown = (e: any) => {
        this.touchPointPosition = {
            top: e.page_y - this.state.position.top,
            right: e.page_x - this.state.position.left,
        };
    };

    private handleTouchEnd = (e: any) => {
        const newPosition = {
            top: e.page_y - this.touchPointPosition.top,
            left: e.page_x - this.touchPointPosition.right,
        };
        sharedPreferences.setObject(VoiceRecordingOverlayViewLocal, JSON.stringify(newPosition));
        this.setState({ position: newPosition });
    };

    private handleTouchMove = (e: any) => {
        const newPosition = {
            top: e.page_y - this.touchPointPosition.top,
            left: e.page_x - this.touchPointPosition.right,
        };
        this.setState({ position: newPosition });
    };

    private handleTouchCancel = () => {
        sharedPreferences.setObject(VoiceRecordingOverlayViewLocal, JSON.stringify(this.state.position));
    };

    // 弹窗按钮处理
    private handleDialogConfirm = () => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        VoiceRecordingOverlayManager.instance.setState({ dialogVisible: false });
        managerState.dialogOnConfirm?.();
    };

    private handleDialogCancel = () => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        VoiceRecordingOverlayManager.instance.setState({ dialogVisible: false });
        managerState.dialogOnCancel?.();
    };

    private prepareRenderProps = () => {
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        const combinedState = {
            ...managerState,
            position: this.state.position,
        };

        return {
            state: combinedState,
            touchPointPosition: this.touchPointPosition,
            onToggleSize: this.handleToggleSize,
            onPauseRecord: this.handleStartPauseRecord,
            onCloseRecord: this.handleCloseRecord,
            onStartRecord: this.handleStartPauseRecord,
            onStopRecord: this.handleStopRecord,
            onTouchDown: this.handleTouchDown,
            onTouchEnd: this.handleTouchEnd,
            onTouchMove: this.handleTouchMove,
            onTouchCancel: this.handleTouchCancel,
            onDialogConfirm: this.handleDialogConfirm,
            onDialogCancel: this.handleDialogCancel,
            setAudioViewRef: (ref: AbcAudioView | null) => {
                this.audioViewRef = ref;
            },
        };
    };

    render() {
        // 使用管理器的状态
        const managerState = VoiceRecordingOverlayManager.instance.getState();
        const { isMinimized, showRecord, showMedicalRecordView } = managerState;

        console.log(TAG, "[RecordDebug] render - state:", {
            isMinimized,
            showRecord,
            showMedicalRecordView,
            isRecording: managerState.isRecording,
            duration: managerState.duration,
        });

        // 准备渲染属性
        const renderProps = this.prepareRenderProps();

        console.log(TAG, "render - returning renderFullscreenView");
        return renderFullscreenView(renderProps);
    }
}
