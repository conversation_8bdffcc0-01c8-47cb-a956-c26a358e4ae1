/**
 * 语音录制控制器
 * 负责管理录制的开始、暂停、停止等操作
 */

import { callNativeWithPromise } from "@hippy/react";
import { StateManager } from "./state-manager";
import { AudioManager } from "./audio-manager";
import { WaveformManager } from "./waveform-manager";
import { Permission, PermissionStatus, PermissionType } from "../../../common-base-module/permission/permission";
import { RECORDING_EVENTS, RECORDING_ACTION_TYPES } from "../utils/constants";
import type { ASRStartResult } from "../utils/types";

const TAG = "RecordingController";

export class RecordingController {
    private stateManager: StateManager;
    private audioManager: AudioManager;
    private waveformManager: WaveformManager;
    private currentSessionId: string | null = "";

    constructor(stateManager: StateManager, audioManager: AudioManager, waveformManager: WaveformManager) {
        this.stateManager = stateManager;
        this.audioManager = audioManager;
        this.waveformManager = waveformManager;
    }

    /**
     * 开始录制
     */
    public async handleStartRecord(): Promise<void> {
        try {
            // 检查麦克风权限
            const hasPermissions = await Permission.checkPermission(PermissionType.microphone, true);
            if (hasPermissions != PermissionStatus.granted && hasPermissions != PermissionStatus.restricted) {
                this.showPermissionDialog(
                    "需要打开麦克风权限",
                    `ABC数字医疗云需要获得您的录制音频权限，以提供语音转病历的功能服务，前往设置打开麦克风权限。`,
                    "去打开",
                    "取消",
                    () => {
                        Permission.openAppSettings();
                    },
                    () => {
                        console.log(TAG, "handleStartRecord - cancel");
                    }
                );
                return;
            }

            const currentState = this.stateManager.getState();

            if (currentState.enableMedicalRecordTranscription) {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.START_RECORDING,
                        businessId: currentState.businessId,
                        businessType: "0",
                    },
                });
            }

            await callNativeWithPromise("AbcASR", "emitMessage", {
                event: RECORDING_EVENTS.RECORDING_ACTION,
                data: {
                    event: RECORDING_ACTION_TYPES.RECORDING_STARTED,
                },
            });

            await callNativeWithPromise("AbcASR", "startService", {});
            const result: ASRStartResult = await callNativeWithPromise("AbcASR", "startRecognize", { audioDataName: "audio-data" });
            this.currentSessionId = result.sessionId;

            // 播放开始录制音效
            this.audioManager.playStartSound();

            this.updateRecordingState(true);
            this.waveformManager.updateWaveformData([]);

            // 重置波形频率控制相关状态
            this.waveformManager.resetWaveformFrequencyControl();

            this.stateManager.setState({
                recognitionResults: [],
                showMedicalRecordView: false,
                showRecord: true,
            });

            // 开始波形动画
            this.waveformManager.startAnimation();
        } catch (e) {
            console.error(TAG, "handleStartRecord error:", e);
        }
    }

    /**
     * 暂停/恢复录制
     */
    public async handleStartPauseRecord(): Promise<void> {
        try {
            const sessionId = this.currentSessionId;
            const currentState = this.stateManager.getState();

            // 如果没有 sessionId，说明还没有开始录制，需要开始录制
            if (!sessionId) {
                await this.handleStartRecord();
                return;
            }

            if (currentState.isPaused) {
                this.audioManager.playStartSound();
                await callNativeWithPromise("AbcASR", "startRecognize", {
                    sessionId: sessionId,
                    audioDataName: "audio-data",
                });

                if (currentState.enableMedicalRecordTranscription) {
                    await callNativeWithPromise("AbcASR", "emitMessage", {
                        event: RECORDING_EVENTS.RECORDING_ACTION,
                        data: {
                            event: RECORDING_ACTION_TYPES.RESUME_RECORDING,
                        },
                    });
                } else {
                    await callNativeWithPromise("AbcASR", "emitMessage", {
                        event: RECORDING_EVENTS.RECORDING_ACTION,
                        data: {
                            event: RECORDING_ACTION_TYPES.RECORDING_RESUMED,
                        },
                    });
                }
                this.waveformManager.startAnimation();
                this.stateManager.setState({ isPaused: false });
            } else {
                // 暂停录制
                console.log(TAG, "Pausing recording");
                this.audioManager.playPauseSound();
                await callNativeWithPromise("AbcASR", "stopRecognize", {
                    sessionId: sessionId,
                });

                if (currentState.enableMedicalRecordTranscription) {
                    await callNativeWithPromise("AbcASR", "emitMessage", {
                        event: RECORDING_EVENTS.RECORDING_ACTION,
                        data: {
                            event: RECORDING_ACTION_TYPES.PAUSE_RECORDING,
                        },
                    });
                } else {
                    await callNativeWithPromise("AbcASR", "emitMessage", {
                        event: RECORDING_EVENTS.RECORDING_ACTION,
                        data: {
                            event: RECORDING_ACTION_TYPES.RECORDING_PAUSED,
                        },
                    });
                }

                this.waveformManager.pauseAnimation();
                this.stateManager.setState({ isPaused: true });
            }
        } catch (e) {
            console.error(TAG, "handleStartPauseRecord error:", e);
        }
    }

    /**
     * 停止录制
     */
    public async handleStopRecord(): Promise<void> {
        try {
            await callNativeWithPromise("AbcASR", "stopService", {});
            await callNativeWithPromise("AbcASR", "stopRecognize", { sessionId: this.currentSessionId });

            // 停止动画循环
            this.waveformManager.pauseAnimation();
            // 重置波形频率控制相关状态
            this.waveformManager.resetWaveformFrequencyControl();

            this.updateRecordingState(false);
            this.stateManager.setState({
                isPaused: false,
                isMinimized: false,
            });
            this.waveformManager.updateWaveformData([]);
            this.currentSessionId = null; // 清空会话ID

            const currentState = this.stateManager.getState();
            if (currentState.enableMedicalRecordTranscription) {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.STOP_RECORDING,
                    },
                });
            } else {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.RECORDING_STOPPED,
                    },
                });
            }
        } catch (e) {
            console.error(TAG, "handleStopRecord error:", e);
        }
    }

    /**
     * 更新录制状态
     */
    private updateRecordingState(isRecording: boolean): void {
        this.stateManager.setState({ isRecording });
    }

    /**
     * 显示权限对话框
     */
    private showPermissionDialog(
        title: string,
        content: string,
        confirmText: string,
        cancelText: string,
        onConfirm: () => void,
        onCancel: () => void
    ): void {
        this.stateManager.setState({
            dialogVisible: true,
            dialogTitle: title,
            dialogContent: content,
            dialogConfirmText: confirmText,
            dialogCancelText: cancelText,
            dialogOnConfirm: onConfirm,
            dialogOnCancel: onCancel,
        });
    }

    /**
     * 获取当前会话ID
     */
    public getCurrentSessionId(): string | null {
        return this.currentSessionId;
    }

    /**
     * 设置当前会话ID
     */
    public setCurrentSessionId(sessionId: string | null): void {
        this.currentSessionId = sessionId;
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        this.currentSessionId = null;
    }
}
