/**
 * 语音录制状态管理器
 * 负责管理录制过程中的所有状态
 */

export interface VoiceRecordingState {
    // 录制相关状态
    isRecording: boolean;
    isPaused: boolean;
    duration: number;
    progress: number;

    // 识别结果状态
    recognitionResults: any[];
    activeSessionId: string | null;
    created: string;

    // 业务相关状态
    taskId: string;
    businessId: string;
    enableMedicalRecordTranscription: boolean;
    chineseExamination: number;
    physicalExamination: number;
    medicalRecordType: number;

    // UI状态
    isMinimized: boolean;
    showRecord: boolean;
    showMedicalRecordView: boolean;

    // 波形数据
    waveformData: number[];

    // 弹窗状态
    toastMessage: string | null;
    toastVisible: boolean;
    dialogVisible: boolean;
    dialogTitle: string | null;
    dialogContent: string | null;
    dialogConfirmText: string | null;
    dialogCancelText: string | null;
    dialogOnConfirm: (() => void) | null;
    dialogOnCancel: (() => void) | null;

    // 动画相关状态
    fullscreenOpacity: number;
    minimizedOpacity: number;

    // 播放控制相关状态
    currentTab: "voice" | "medical";
    medicalRecordData: string;
    isTranscribing: boolean;
    voiceRecordResult: any;
    voiceRecordSegments: any[];
    currentPlayTime: number;
    totalDuration: number;
    isPlaying: boolean;
    playbackSpeed: number;
    highlightedSegmentId: string | null;
    synthesizedFilePath: string;
    patient: any;
}

export class StateManager {
    private state: VoiceRecordingState;
    private asrRecordingRef: any = null;
    private minimizedOverlayRef: any = null;
    private asrRecordRef: any = null;

    constructor() {
        this.state = this.getInitialState();
    }

    private getInitialState(): VoiceRecordingState {
        return {
            // 录制相关状态
            isRecording: false,
            isPaused: false,
            duration: 0,
            progress: 0,

            // 识别结果状态
            recognitionResults: [],
            activeSessionId: null,
            created: "",

            // 业务相关状态
            taskId: "",
            businessId: "",
            enableMedicalRecordTranscription: false,
            chineseExamination: 0,
            physicalExamination: 0,
            medicalRecordType: 0,

            // UI状态
            isMinimized: false,
            showRecord: false,
            showMedicalRecordView: false,

            // 波形数据
            waveformData: [],

            // 弹窗状态
            toastMessage: null,
            toastVisible: false,
            dialogVisible: false,
            dialogTitle: null,
            dialogContent: null,
            dialogConfirmText: null,
            dialogCancelText: null,
            dialogOnConfirm: null,
            dialogOnCancel: null,

            // 动画相关状态
            fullscreenOpacity: 1,
            minimizedOpacity: 0,

            // 播放控制相关状态
            currentTab: "medical",
            medicalRecordData: "",
            isTranscribing: true,
            voiceRecordResult: {},
            voiceRecordSegments: [],
            currentPlayTime: 0,
            totalDuration: 0,
            isPlaying: false,
            playbackSpeed: 1,
            highlightedSegmentId: null,
            synthesizedFilePath: "",
            patient: {},
        };
    }

    public getState(): VoiceRecordingState {
        return { ...this.state };
    }

    public setState(newState: Partial<VoiceRecordingState>, callback?: () => void): void {
        console.log("StateManager", "[RecordDebug] setState called with:", Object.keys(newState), newState);
        this.state = { ...this.state, ...newState };

        // 同步状态到 VoiceRecordingPage 组件
        if (this.asrRecordingRef && this.asrRecordingRef.setState) {
            console.log("StateManager", "[RecordDebug] Syncing to voiceRecordingRef");
            this.asrRecordingRef.setState(newState, callback);
        } else {
            console.log("StateManager", "[RecordDebug] No voiceRecordingRef available");
            if (callback) {
                callback();
            }
        }

        console.log("StateManager", "[RecordDebug] Syncing to asrOverlayRef", this.asrRecordRef);
        if (this.asrRecordRef && this.asrRecordRef.setState) {
            this.asrRecordRef.setState(newState);
        }

        if (this.minimizedOverlayRef && this.minimizedOverlayRef.setState) {
            this.minimizedOverlayRef.setState({
                forceUpdate: Date.now(),
            });
        }

        if (newState.waveformData !== undefined) {
            console.log("StateManager", "[WaveDebug] Waveform data updated", {
                length: newState.waveformData.length,
                sample: newState.waveformData.slice(-3), // 最后3个数据点
                minimizedOverlayRef: !!this.minimizedOverlayRef,
                overlayRef: !!this.asrRecordingRef,
                asrRecordRef: !!this.asrRecordRef,
            });
        }
    }

    public updateState(updater: (prevState: VoiceRecordingState) => Partial<VoiceRecordingState>, callback?: () => void): void {
        const newState = updater(this.state);
        this.setState(newState, callback);
    }

    public resetState(): void {
        this.state = this.getInitialState();
    }

    public setAsrRecordingRef(ref: any): void {
        this.asrRecordingRef = ref;
    }

    public setMinimizedOverlayRef(ref: any): void {
        this.minimizedOverlayRef = ref;
    }

    public getMinimizedOverlayRef(): any {
        return this.minimizedOverlayRef;
    }

    public setAsrRecordRef(ref: any): void {
        this.asrRecordRef = ref;
    }

    public getAsrRecordingRef(): any {
        return this.asrRecordRef;
    }
}
