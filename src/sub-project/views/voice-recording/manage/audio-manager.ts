/**
 * 语音录制音效管理器
 * 负责管理录制过程中的音效播放
 */

import { StateManager } from "./state-manager";
import { AUDIO_WAV } from "../utils/constants";

const TAG = "AudioManager";

export class AudioManager {
    private stateManager: StateManager;

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    /**
     * 播放音效
     * @param type 音效类型：start | pause | stop
     */
    public playSound(type: "start" | "pause" | "stop"): void {
        try {
            // 检查是否有音频视图引用
            const overlayRef = this.stateManager.getAsrRecordingRef();
            const minimizedOverlayRef = this.stateManager.getMinimizedOverlayRef();
            const audioViewRef = overlayRef?.audioViewRef || minimizedOverlayRef?.audioViewRef;
            if (!audioViewRef) {
                console.log(TAG, "No audio view ref available for playing sound");
                return;
            }

            let soundFile = "";
            switch (type) {
                case "start":
                    soundFile = AUDIO_WAV.ANTHRO_SET3_START;
                    break;
                case "pause":
                    soundFile = AUDIO_WAV.ANTHRO_SET3_PAUSE;
                    break;
                case "stop":
                    soundFile = AUDIO_WAV.ANTHRO_SET3_PAUSE; // 使用暂停音效作为停止音效
                    break;
            }

            if (soundFile) {
                console.log(TAG, "Playing sound:", soundFile);
                if (overlayRef) {
                    overlayRef?.audioViewRef?.play(soundFile);
                }
                if (minimizedOverlayRef) {
                    minimizedOverlayRef?.audioViewRef?.play(soundFile);
                }
            }
        } catch (error) {
            console.error(TAG, "Error playing sound:", error);
        }
    }

    /**
     * 播放开始录制音效
     */
    public playStartSound(): void {
        this.playSound("start");
    }

    /**
     * 播放暂停录制音效
     */
    public playPauseSound(): void {
        this.playSound("pause");
    }

    /**
     * 播放停止录制音效
     */
    public playStopSound(): void {
        this.playSound("stop");
    }

    /**
     * 检查音频视图是否可用
     */
    public isAudioViewAvailable(): boolean {
        const overlayRef = this.stateManager.getAsrRecordingRef();
        return !!(overlayRef && overlayRef.audioViewRef);
    }
}
