/**
 * 导航管理器
 * 负责管理页面导航和浮窗显示
 */

import React from "react";
import { StateManager } from "./state-manager";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import abcOverlay, { OverlayViewKey } from "../../../base-ui/views/abc-overlay";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";

const TAG = "NavigationManager";

export class NavigationManager {
    private stateManager: StateManager;
    private VoiceRecordingOverlayViewComponent: React.ComponentType<any> | undefined;
    private recordRef: any = null;
    private managerRef: any = null; // 管理器引用

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
    }

    /**
     * 设置管理器引用
     */
    public setManagerRef(managerRef: any): void {
        this.managerRef = managerRef;
    }

    /**
     * 设置语音录制浮窗组件
     */
    public setVoiceRecordingOverlayViewComponent(component: React.ComponentType<any>): void {
        console.log(TAG, "[InitDebug] setVoiceRecordingOverlayViewComponent called with component:", !!component);
        this.VoiceRecordingOverlayViewComponent = component;
    }

    /**
     * 设置录制页面引用
     */
    public setRecordRef(ref: any): void {
        this.recordRef = ref;
    }

    /**
     * 获取录制页面引用
     */
    public getRecordingRef(): any {
        return this.recordRef;
    }

    /**
     * 显示最小化浮窗
     */
    public showMinimizedOverlay(): void {
        try {
            this.stateManager.setState({
                minimizedOpacity: 1,
            });
            const VoiceRecordingMinimizedOverlay = require("../pages/voice-recording-minimized-overlay").default;

            const minimizedOverlayView = React.createElement(VoiceRecordingMinimizedOverlay);
            const overlayKey = OverlayViewKey.voiceRecordingOverlay;
            abcOverlay.show(minimizedOverlayView, overlayKey);
        } catch (error) {
            console.error(TAG, "Error in showMinimizedOverlay:", error);
        }
    }

    /**
     * 隐藏最小化浮窗
     */
    public hideMinimizedOverlay(): void {
        abcOverlay.hide(OverlayViewKey.voiceRecordingOverlay);
    }

    /**
     * 显示全屏视图
     */
    public showFullscreenView(): void {
        if (!this.VoiceRecordingOverlayViewComponent) {
            return;
        }
        const overlayView = React.createElement(this.VoiceRecordingOverlayViewComponent, {
            ref: (ref: any) => this.stateManager.setAsrRecordingRef(ref),
        });
        ABCNavigator.navigateToPage(overlayView);
    }

    /**
     * 导航到病历转写页面
     */
    public async navigateMedicalRecordPage(): Promise<void> {
        const renderProps = this.prepareRenderProps();
        const { ABCNavigator } = require("../../../base-ui/views/abc-navigator");
        const { MedicalRecordPage } = require("../pages/medical-record-page");

        ABCNavigator.navigateToPage(
            React.createElement(MedicalRecordPage, {
                ref: (ref: any) => this.setRecordRef(ref),
                ...renderProps,
                enableGestureRecognizer: false,
            })
        );
    }

    /**
     * 导航到对话框页面
     */
    public async navigateDialog(props: { title: string; content: string; icon?: string }): Promise<void> {
        const { DialogBuilder } = require("../../../base-ui/dialog/dialog-builder");
        const { Sizes } = require("../../../theme");

        const dialog = new DialogBuilder();
        dialog.title = props.title;
        dialog.content = props.content;
        dialog.isShowBtn = false;
        dialog.icon = props.icon;
        dialog.iconColor = "#08BB88";
        dialog.show({
            borderRadius: Sizes.dp12,
        });
    }

    /**
     * 返回录制页面
     */
    public handleBackToRecord = (): void => {
        ABCNavigator.pop();
        delayed(100)
            .toPromise()
            .then(() => {
                this.stateManager.setState({
                    showMedicalRecordView: false,
                    showRecord: true,
                });
                // 重新显示录制页面
                this.managerRef?.show(true);
            });
    };

    /**
     * 隐藏当前页面
     */
    public hide(): void {
        this.hideMinimizedOverlay();
        ABCNavigator.pop();
    }

    /**
     * 准备渲染属性的辅助方法
     */
    private prepareRenderProps(): any {
        const overlayRef = this.stateManager.getAsrRecordingRef();
        if (!overlayRef) {
            return {};
        }

        const currentState = this.stateManager.getState();
        return {
            state: currentState, // 使用管理器的状态
            touchPointPosition: overlayRef.touchPointPosition,
            onToggleSize: overlayRef.handleToggleSize,
            onPauseRecord: overlayRef.handleStartPauseRecord,
            onCloseRecord: overlayRef.handleCloseRecord,
            onStartRecord: overlayRef.props.onStartRecord,
            onStopRecord: overlayRef.handleStopRecord,
            onTouchDown: overlayRef.handleTouchDown,
            onTouchEnd: overlayRef.handleTouchEnd,
            onTouchMove: overlayRef.handleTouchMove,
            onTouchCancel: overlayRef.handleTouchCancel,
            onDialogConfirm: overlayRef.handleDialogConfirm,
            onDialogCancel: overlayRef.handleDialogCancel,
            fullscreenOpacityAnimation: overlayRef.fullscreenOpacityAnimation,
            minimizedOpacityAnimation: overlayRef.minimizedOpacityAnimation,
        };
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        this.VoiceRecordingOverlayViewComponent = undefined;
        this.recordRef = null;
    }
}
