/**
 * ASR事件处理器
 * 负责处理ASR相关的事件和回调
 */

import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import { StateManager } from "./state-manager";
import { WaveformManager } from "./waveform-manager";
import { ASR_CALLBACK, CALL_STATE, DATA_EVENTS, RECORDING_ACTION_TYPES, RECORDING_EVENTS } from "../utils/constants";
import type { AbcASTSocketOn } from "../utils/types";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";
import { LoadingDialog } from "../../../base-ui/dialog/loading-dialog";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { AbcPlatformMix } from "../../../base-business/native-modules/abc-platform-mix";

const TAG = "ASREventHandler";

export class ASREventHandler {
    private stateManager: StateManager;
    private waveformManager: WaveformManager;
    private hippyEventEmitter: HippyEventEmitter | null = null;
    private callState = false;
    private managerRef: any = null; // 管理器引用
    private loadingDialog = new LoadingDialog("正在识别");

    constructor(stateManager: StateManager, waveformManager: WaveformManager) {
        this.stateManager = stateManager;
        this.waveformManager = waveformManager;
    }

    /**
     * 设置管理器引用
     */
    public setManagerRef(managerRef: any): void {
        this.managerRef = managerRef;
    }

    /**
     * 注册Socket事件监听器
     */
    public registerSocketEventListeners(): void {
        callNativeWithPromise("AbcASR", "eventOn", { event: RECORDING_EVENTS.RECORDING_ACTION });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.ASR_RESULT });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.ASR_COMPLETED });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.WAVEFORM_DATA });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.STOP_WATCH });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.EVENT_CONNECT });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.EVENT_DISCONNECT });
        callNativeWithPromise("AbcASR", "listenerCall", {});
    }

    /**
     * 注册ASR事件监听器
     */
    public registerASREventListeners(): void {
        this.hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitter.addListener("AbcASRSocketOn", this.handleSocketResult.bind(this));
        this.hippyEventEmitter.addListener("AbcASRCallback", this.handleAsrCallback.bind(this));
    }

    /**
     * 处理Socket结果
     */
    public handleSocketResult(evt: AbcASTSocketOn): void {
        console.log(TAG, "handleSocketResult:", JSON.stringify(evt));
        const { event, message } = evt;

        if (event === DATA_EVENTS.ASR_RESULT) {
            const messageObj = JSON.parse(message);
            this.handleASRResult(messageObj);
        } else if (event === DATA_EVENTS.WAVEFORM_DATA) {
            const messageObj = JSON.parse(message);
            console.log(TAG, "handleSocketResult - waveform-data:", messageObj);
        } else if (event === RECORDING_EVENTS.RECORDING_ACTION) {
            const messageObj = JSON.parse(message);
            this.handleRecordingAction(messageObj);
        } else if (event === DATA_EVENTS.ASR_COMPLETED) {
            console.log(TAG, "handleSocketResult - asr-completed");
        } else if (event === DATA_EVENTS.STOP_WATCH) {
            if (DeviceUtils.isOhos()) {
                const messageObj = JSON.parse(message);
                this.stateManager.setState({ duration: messageObj?.message / 1000 });
            } else {
                this.stateManager.setState({ duration: Number(message) / 1000 });
            }
        } else if (event === DATA_EVENTS.EVENT_CONNECT) {
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: DATA_EVENTS.APP_READY,
                data: {},
            });
        } else if (event === DATA_EVENTS.EVENT_DISCONNECT) {
            this.stateManager.getAsrRecordingRef()?.handleStopRecord(false);
            // delayed(100).subscribe(() => {
            //     showConfirmDialog("上次语音病历录制中断", "已自动帮您保存录音，可继续录制", undefined, "rgba(0,0,0,0.8)").then();
            // });
        }
    }

    /**
     * 处理ASR回调
     */
    public handleAsrCallback(evt: any): void {
        console.log(TAG, "handleAsrCallback:", evt);
        const { event, payload } = evt;

        if (event === ASR_CALLBACK.ON_WAVEFORM_DATA) {
            const dataArray = payload.data || [];
            console.log(TAG, "[WaveDebug]handleAsrCallback - waveform data array:", dataArray, "points", "event:", event);

            // 记录数据接收时间戳
            const currentTime = Date.now();

            // 处理波形数据，应用平台频率统一逻辑
            this.waveformManager.processWaveformDataWithFrequencyControl(dataArray, currentTime);

            // 启动动画循环（如果尚未启动）
            this.waveformManager.startAnimation();

            // 发送波形数据事件
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: DATA_EVENTS.WAVEFORM_DATA,
                data: {
                    waveform_data: dataArray,
                },
            });
        }

        if (event === ASR_CALLBACK.ON_CALL_STATE_CHANGED) {
            this.handleCallStateChanged(payload);
        }
    }

    /**
     * 处理ASR识别结果
     */
    public handleASRResult(result: any): void {
        const { code, message } = result;
        if (code != 0 || message != "success") {
            return;
        }

        if (!result.result) {
            return;
        }

        const { sliceType, voiceText } = result.result;
        console.log(TAG, "handleASRResult:", result);

        this.stateManager.updateState(
            (prevState) => ({
                recognitionResults: [
                    ...prevState.recognitionResults.filter((item: any) => item.isFinal),
                    {
                        text: voiceText || "",
                        isFinal: sliceType == 2,
                    },
                ],
            }),
            () => {
                console.log("临时结果更新:", this.stateManager.getState().recognitionResults);
            }
        );
    }

    /**
     * 处理录制动作
     */
    private async handleRecordingAction(args: any): Promise<void> {
        const { event, data } = args;
        console.log("收到控制指令:", event, data);

        if (!event) {
            return;
        }

        // 根据事件类型处理不同的录制动作
        if (event === RECORDING_ACTION_TYPES.START_RECORDING) {
            const appIsBackOrFront = await AbcPlatformMix.appIsFront();
            if (!appIsBackOrFront) {
                return;
            }
            const currentState = this.stateManager.getState();
            if (currentState.isRecording) {
                return;
            }
            // 启动录制
            const isShow = await this.managerRef?.show(false);
            if (!isShow) return;
            if (currentState.isMinimized && this.stateManager.getAsrRecordingRef()) {
                this.stateManager.getAsrRecordingRef().handleToggleSize();
            }
            this.managerRef?.handleStartRecord();
        } else if (event === RECORDING_ACTION_TYPES.RECORDING_STARTED) {
            // 录制已开始
            const { taskId, businessId } = data;
            this.handleStarted(taskId, businessId);
        } else if (event === RECORDING_ACTION_TYPES.STOP_RECORDING) {
            // 停止录制
            this.managerRef?.handleStopRecord(true);
        } else if (event === RECORDING_ACTION_TYPES.RECORDING_STOPPED) {
            // 录制已停止
            const currentState = this.stateManager.getState();
            if (currentState.enableMedicalRecordTranscription && currentState.showRecord) {
                await this.loadingDialog.hide();
                await delayed(50).toPromise();
                await this.managerRef?.handleASRCompleted();
            }
        } else if (event === RECORDING_ACTION_TYPES.PAUSE_RECORDING) {
            // 暂停录制
            this.managerRef?.handleStartPauseRecord();
        } else if (event === RECORDING_ACTION_TYPES.RESUME_RECORDING) {
            // 恢复录制
            this.managerRef?.handleStartPauseRecord();
        }
    }

    /**
     * 处理通话状态变化
     */
    private handleCallStateChanged(payload: any): void {
        const overlayRef = this.stateManager.getAsrRecordingRef();

        if (payload.state === CALL_STATE.OFFHOOK) {
            this.callState = true;
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: RECORDING_EVENTS.RECORDING_ACTION,
                data: {
                    event: RECORDING_ACTION_TYPES.RECORDING_INTERRUPTED,
                },
            });
            if (overlayRef && overlayRef.handleStartPauseRecord) {
                overlayRef.handleStartPauseRecord();
            }
        }

        if (payload.state === CALL_STATE.IDLE && this.callState) {
            this.callState = false;
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: RECORDING_EVENTS.RECORDING_ACTION,
                data: {
                    event: RECORDING_ACTION_TYPES.RESUME_RECORDING,
                },
            });
            if (overlayRef && overlayRef.handleStartPauseRecord) {
                overlayRef.handleStartPauseRecord();
            }
        }
    }

    /**
     * 处理录制开始
     */
    private handleStarted(taskId: string, businessId: string): void {
        console.log(TAG, "handleJoin:", taskId, businessId);
        this.stateManager.setState({
            taskId,
            businessId,
        });
    }

    /**
     * 清理事件监听器
     */
    public cleanup(): void {
        if (this.hippyEventEmitter) {
            this.hippyEventEmitter.removeAllListeners("AbcASRCallback");
            this.hippyEventEmitter.removeAllListeners("AbcASRSocketOn");
            this.hippyEventEmitter = null;
        }
        this.callState = false;
    }

    /**
     * 获取事件发射器
     */
    public getEventEmitter(): HippyEventEmitter | null {
        return this.hippyEventEmitter;
    }
}
